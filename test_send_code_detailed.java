import javax.net.ssl.*;
import java.io.*;
import java.net.*;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;
import java.util.Scanner;

public class test_send_code_detailed {
    
    private static final String BASE_URL = "https://io661.com/";
    
    public static void main(String[] args) {
        System.out.println("=== 详细测试发送验证码功能 ===");
        
        // 设置系统属性
        System.setProperty("javax.net.ssl.trustStore", "");
        System.setProperty("javax.net.ssl.trustStorePassword", "");
        System.setProperty("com.sun.net.ssl.checkRevocation", "false");
        System.setProperty("sun.security.ssl.allowUnsafeRenegotiation", "true");
        
        // 配置SSL
        setupSSL();
        
        // 获取用户输入的手机号
        Scanner scanner = new Scanner(System.in);
        System.out.print("请输入要测试的手机号（11位数字）: ");
        String phone = scanner.nextLine().trim();
        
        if (phone.isEmpty()) {
            phone = "13800138000"; // 默认测试号码
            System.out.println("使用默认测试手机号: " + phone);
        }
        
        // 验证手机号格式
        if (!phone.matches("\\d{11}")) {
            System.err.println("手机号格式不正确，应为11位数字");
            return;
        }
        
        // 测试不同的请求参数
        testSendCodeWithDifferentParams(phone);
        
        scanner.close();
    }
    
    private static void setupSSL() {
        try {
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };
            
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
            
            // 设置为默认SSL上下文
            HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
            
            System.out.println("✓ SSL配置完成");
            
        } catch (Exception e) {
            System.err.println("✗ SSL配置失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testSendCodeWithDifferentParams(String phone) {
        System.out.println("\n=== 测试不同的请求参数 ===");
        
        // 测试1: 标准参数
        System.out.println("\n1. 测试标准参数 (type=0):");
        testSendCode(phone, 0);
        
        // 测试2: 不同的type值
        System.out.println("\n2. 测试不同type值 (type=1):");
        testSendCode(phone, 1);
        
        // 测试3: 添加更多请求头
        System.out.println("\n3. 测试添加更多请求头:");
        testSendCodeWithHeaders(phone);
        
        // 测试4: 测试服务器连通性
        System.out.println("\n4. 测试服务器连通性:");
        testServerConnectivity();
    }
    
    private static void testSendCode(String phone, int type) {
        try {
            // 构建请求URL
            String endpoint = "web/sms";
            URL url = new URL(BASE_URL + endpoint);
            
            // 创建连接
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("User-Agent", "IO661Extension/1.0");
            connection.setDoOutput(true);
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            
            // 构建请求体
            String jsonBody = String.format("{\"phone\":\"%s\",\"type\":%d}", phone, type);
            System.out.println("  请求体: " + jsonBody);
            
            // 发送请求
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonBody.getBytes("utf-8");
                os.write(input, 0, input.length);
            }
            
            // 获取响应
            int responseCode = connection.getResponseCode();
            String response = readResponse(connection);
            
            System.out.println("  响应码: " + responseCode);
            System.out.println("  响应内容: " + response);
            
            connection.disconnect();
            
        } catch (Exception e) {
            System.err.println("  ✗ 请求失败: " + e.getMessage());
        }
    }
    
    private static void testSendCodeWithHeaders(String phone) {
        try {
            // 构建请求URL
            String endpoint = "web/sms";
            URL url = new URL(BASE_URL + endpoint);
            
            // 创建连接
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            connection.setRequestProperty("Accept", "application/json, text/plain, */*");
            connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            connection.setRequestProperty("Cache-Control", "no-cache");
            connection.setRequestProperty("Pragma", "no-cache");
            connection.setDoOutput(true);
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            
            // 构建请求体
            String jsonBody = String.format("{\"phone\":\"%s\",\"type\":0}", phone);
            System.out.println("  请求体: " + jsonBody);
            
            // 发送请求
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonBody.getBytes("utf-8");
                os.write(input, 0, input.length);
            }
            
            // 获取响应
            int responseCode = connection.getResponseCode();
            String response = readResponse(connection);
            
            System.out.println("  响应码: " + responseCode);
            System.out.println("  响应内容: " + response);
            
            connection.disconnect();
            
        } catch (Exception e) {
            System.err.println("  ✗ 请求失败: " + e.getMessage());
        }
    }
    
    private static void testServerConnectivity() {
        try {
            // 测试根路径
            URL url = new URL(BASE_URL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            
            int responseCode = connection.getResponseCode();
            System.out.println("  服务器根路径响应码: " + responseCode);
            
            if (responseCode == 200) {
                System.out.println("  ✓ 服务器连通正常");
            } else {
                System.out.println("  ⚠ 服务器响应异常");
            }
            
            connection.disconnect();
            
        } catch (Exception e) {
            System.err.println("  ✗ 服务器连通性测试失败: " + e.getMessage());
        }
    }
    
    private static String readResponse(HttpsURLConnection connection) throws IOException {
        int responseCode = connection.getResponseCode();
        InputStream inputStream = responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream();
        
        if (inputStream == null) {
            return "";
        }
        
        StringBuilder response = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "utf-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
        }
        
        return response.toString();
    }
}
