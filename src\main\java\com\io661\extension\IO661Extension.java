package com.io661.extension;

import com.io661.extension.controller.LoginController;
import com.io661.extension.controller.MainWindowController;
import com.io661.extension.service.Impl.UserServiceImpl;
import com.io661.extension.service.UserService;
import com.io661.extension.util.User.UserCookieManager;
import com.io661.extension.util.SSLCertificateManager;
import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import lombok.Getter;

import java.io.IOException;

public class IO661Extension extends Application {
    /**
     * -- GETTER --
     *  获取主窗口实例
     */
    @Getter
    private static Stage mainStage;
    /**
     * -- GETTER --
     *  获取登录窗口实例
     */
    @Getter
    private static Stage loginStage;

    /**
     * 关闭登录窗口
     */
    public static void closeLoginStage() {
        if (loginStage != null && loginStage.isShowing()) {
            loginStage.close();
            System.out.println("登录窗口已关闭");
        }
    }

    @Override
    public void start(Stage primaryStage) throws IOException {
        // 在应用启动时立即初始化SSL配置
        try {
            SSLCertificateManager.initializeSSLContext();
            System.out.println("应用启动时SSL配置初始化成功");
        } catch (Exception e) {
            System.err.println("应用启动时SSL配置初始化失败: " + e.getMessage());
            e.printStackTrace();
        }

        // 保存主窗口引用
        mainStage = primaryStage;

        // 加载主窗口
        FXMLLoader mainLoader = new FXMLLoader(IO661Extension.class.getResource("/com/io661/extension/fxml/main-window.fxml"));
        Parent mainRoot = mainLoader.load();
        Scene mainScene = new Scene(mainRoot, 1366, 768);

        // 设置主窗口
        mainStage.setTitle("IO661增值服务");
        mainStage.setScene(mainScene);
        mainStage.setResizable(true);

        // 创建登录窗口
        loginStage = new Stage();
        loginStage.initOwner(mainStage);
        // 移除模态设置，允许与主窗口交互
        // loginStage.initModality(Modality.APPLICATION_MODAL);
        loginStage.initStyle(StageStyle.TRANSPARENT); // 完全透明窗口，没有装饰和背景
        // 设置登录窗口关闭事件处理
        loginStage.setOnCloseRequest(event -> {
            System.out.println("登录窗口关闭请求");
            // 允许关闭登录窗口，但不退出整个应用程序
            loginStage.hide();
            // 移除主窗口的变暗效果
            mainRoot.setEffect(null);
        });

        // 加载登录窗口
        FXMLLoader loginLoader = new FXMLLoader(IO661Extension.class.getResource("/com/io661/extension/fxml/login.fxml"));
        Parent loginRoot = loginLoader.load();
        Scene loginScene = new Scene(loginRoot);

        // 设置登录窗口透明背景
        loginScene.setFill(javafx.scene.paint.Color.TRANSPARENT);

        // 设置登录窗口
        loginStage.setTitle("IO661增值服务用户登录");
        loginStage.setScene(loginScene);
        loginStage.setResizable(false);

        // 创建半透明遮罩效果
        javafx.scene.effect.ColorAdjust colorAdjust = new javafx.scene.effect.ColorAdjust();
        colorAdjust.setBrightness(-0.6); // 降低亮度，使主窗口变暗

        // 先显示主窗口，再显示登录窗口
        mainStage.show();

        if (UserCookieManager.tokenFileExists()) {
            String savedToken = UserCookieManager.readToken();
            if (savedToken != null && !savedToken.isEmpty()) {

                try {
                    // 验证令牌有效性
                    UserService userService = new UserServiceImpl();

                    // 在验证前，先设置全局令牌，确保CommonHttpUrl可以正确使用它
                    LoginController.setAuthToken(savedToken);

                    boolean isValid = userService.validateToken(savedToken);

                    if (isValid) {

                        // 更新主界面
                        MainWindowController mainController = MainWindowController.getInstance();
                        if (mainController != null) {
                            mainController.updateAfterLogin();
                        }

                        // 不显示登录窗口
                        return;
                    } else {
                        // 清除全局令牌
                        LoginController.setAuthToken(null);
                        // 删除无效令牌
                        UserCookieManager.deleteToken();
                    }
                } catch (Exception e) {
                    System.err.println("验证令牌时发生错误: " + e.getMessage());
                    // 清除全局令牌
                    LoginController.setAuthToken(null);
                    // 删除无效令牌
                    UserCookieManager.deleteToken();
                }
            } else {
                System.out.println("令牌文件存在但内容无效，需要登录");
                // 删除无效令牌文件
                UserCookieManager.deleteToken();
            }
        } else {
            System.out.println("未找到保存的授权令牌文件，需要登录");
        }


        // 在显示登录窗口前，给主窗口添加变暗效果
        mainRoot.setEffect(colorAdjust);

        // 显示登录窗口
        loginStage.show();

        // 在窗口显示后，调整位置使其居中
        // 使用Platform.runLater确保在JavaFX应用线程上执行，并且在窗口完全加载后
        javafx.application.Platform.runLater(() -> {
            double centerXPosition = mainStage.getX() + mainStage.getWidth()/2 - loginStage.getWidth()/2;
            double centerYPosition = mainStage.getY() + mainStage.getHeight()/2 - loginStage.getHeight()/2;
            loginStage.setX(centerXPosition);
            loginStage.setY(centerYPosition);

            // 打印窗口信息，用于调试
            System.out.println("登录窗口位置已调整为居中");
            System.out.println("登录窗口大小: " + loginStage.getWidth() + "x" + loginStage.getHeight());
            System.out.println("主窗口大小: " + mainStage.getWidth() + "x" + mainStage.getHeight());
        });

        // 当登录窗口关闭时，移除主窗口的变暗效果
        loginStage.setOnHidden(event -> mainRoot.setEffect(null));
    }

    public static void main(String[] args) {
        // 设置系统属性以支持HTTPS连接
        System.setProperty("javax.net.ssl.trustStore", "");
        System.setProperty("javax.net.ssl.trustStorePassword", "");
        System.setProperty("com.sun.net.ssl.checkRevocation", "false");
        System.setProperty("sun.security.ssl.allowUnsafeRenegotiation", "true");

        // 初始化SSL配置
        try {
            SSLCertificateManager.initializeSSLContext();
            System.out.println("主方法中SSL配置初始化成功");
        } catch (Exception e) {
            System.err.println("主方法中SSL配置初始化失败: " + e.getMessage());
        }

        launch(args);
    }
}