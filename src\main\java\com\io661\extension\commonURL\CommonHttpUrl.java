package com.io661.extension.commonURL;

import com.io661.extension.commonResult.CommonShowAlert;
import javafx.scene.control.Alert;
import lombok.Getter;
import lombok.Setter;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import javax.net.ssl.HttpsURLConnection;
import com.io661.extension.util.SSLCertificateManager;

public class CommonHttpUrl {
    private final String BASE_URL = "https://io661.com/";  // 生产环境URL
    /**
     * -- SETTER --
     *  设置授权令牌
     * -- GETTER --
     *  获取授权令牌
     *
     */
    //  private final String BASE_URL = "http://192.168.0.110:8661/";  // 开发环境URL
    @Getter
    @Setter
    private String authToken; // 存储授权令牌

    // 静态初始化块，确保SSL配置在类加载时就完成
    static {
        try {
            SSLCertificateManager.initializeSSLContext();
        } catch (Exception e) {
            System.err.println("CommonHttpUrl静态初始化SSL失败: " + e.getMessage());
        }
    }

    /**
     * 发送GET请求
     *
     * @param endpoint 接口路径
     * @param params 请求参数
     * @param headers 请求头
     * @return 响应结果字符串
     * @throws IOException 请求异常
     */
    public String doGet(String endpoint, Map<String, String> params, Map<String, String> headers) throws IOException {
        StringBuilder urlBuilder = new StringBuilder(BASE_URL + endpoint);

        // 添加请求参数
        if (params != null && !params.isEmpty()) {
            urlBuilder.append("?");
            for (Map.Entry<String, String> entry : params.entrySet()) {
                urlBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
            urlBuilder.deleteCharAt(urlBuilder.length() - 1); // 删除最后一个&
        }

        URL url = URI.create(urlBuilder.toString()).toURL();
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 配置HTTPS连接的SSL证书（强化版，针对打包后的环境）
        if (connection instanceof HttpsURLConnection) {
            HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
            try {
                // 确保SSL上下文已初始化
                SSLCertificateManager.initializeSSLContext();

                // 强制设置SSL配置
                forceSSLConfiguration(httpsConnection);

                System.out.println("GET请求SSL配置成功");
            } catch (Exception e) {
                System.err.println("GET请求SSL配置失败: " + e.getMessage());
                e.printStackTrace();
            }
        }

        connection.setRequestMethod("GET");

        // 如果headers为null，创建一个新的Map
        if (headers == null) {
            headers = new HashMap<>();
        }

        // 添加Authorization Cookie
        addAuthorizationCookie(headers, authToken);

        // 添加请求头
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            connection.setRequestProperty(entry.getKey(), entry.getValue());
        }

        return getResponse(connection);
    }

    /**
     * 发送POST请求
     *
     * @param endpoint 接口路径
     * @param jsonBody JSON格式的请求体
     * @param headers 请求头
     * @return 响应结果字符串
     * @throws IOException 请求异常
     */
    public String doPost(String endpoint, String jsonBody, Map<String, String> headers) throws IOException {
        URL url = URI.create(BASE_URL + endpoint).toURL();
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();


        CommonShowAlert.showAlert(Alert.AlertType.INFORMATION, "开始", "1");
        // 配置HTTPS连接的SSL证书（强化版）
        if (connection instanceof HttpsURLConnection) {
            HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
            try {
                // 确保SSL上下文已初始化
                SSLCertificateManager.initializeSSLContext();

                // 强制设置SSL配置
                forceSSLConfiguration(httpsConnection);

                System.out.println("POST请求SSL配置成功");
            } catch (Exception e) {
                System.err.println("POST请求SSL配置失败: " + e.getMessage());
                e.printStackTrace();
            }
        }

        CommonShowAlert.showAlert(Alert.AlertType.INFORMATION, "开始", "2");
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        // 如果headers为null，创建一个新的Map
        if (headers == null) {
            headers = new HashMap<>();
        }

        // 添加Authorization Cookie
        addAuthorizationCookie(headers, authToken);

        CommonShowAlert.showAlert(Alert.AlertType.INFORMATION, "开始", "3");
        // 添加请求头
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            connection.setRequestProperty(entry.getKey(), entry.getValue());
        }

        CommonShowAlert.showAlert(Alert.AlertType.INFORMATION, "开始", "4");
        // 写入请求体
        if (jsonBody != null && !jsonBody.isEmpty()) {
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }catch (IOException e) {
                CommonShowAlert.showAlert(Alert.AlertType.INFORMATION, "问题", e.getMessage());
            }
        }

        CommonShowAlert.showAlert(Alert.AlertType.INFORMATION, "开始", "5");
        return getResponse(connection);
    }

    /**
     * 发送PUT请求
     *
     * @param endpoint 接口路径
     * @param jsonBody JSON格式的请求体
     * @param headers 请求头
     * @return 响应结果字符串
     * @throws IOException 请求异常
     */
    public String doPut(String endpoint, String jsonBody, Map<String, String> headers) throws IOException {
        URL url = URI.create(BASE_URL + endpoint).toURL();
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 配置HTTPS连接的SSL证书（强化版）
        if (connection instanceof HttpsURLConnection) {
            HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
            try {
                // 确保SSL上下文已初始化
                SSLCertificateManager.initializeSSLContext();

                // 强制设置SSL配置
                forceSSLConfiguration(httpsConnection);

                System.out.println("PUT请求SSL配置成功");
            } catch (Exception e) {
                System.err.println("PUT请求SSL配置失败: " + e.getMessage());
                e.printStackTrace();
            }
        }

        connection.setRequestMethod("PUT");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        // 如果headers为null，创建一个新的Map
        if (headers == null) {
            headers = new HashMap<>();
        }

        // 如果authToken不为空，添加Authorization作为Cookie
        if (authToken != null && !authToken.isEmpty() && !headers.containsKey("Cookie")) {
            // 将Authorization作为Cookie发送，与Apifox保持一致
            headers.put("Cookie", "Authorization=" + authToken);
        }

        // 添加请求头
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            connection.setRequestProperty(entry.getKey(), entry.getValue());
        }

        // 写入请求体
        if (jsonBody != null && !jsonBody.isEmpty()) {
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
        }

        return getResponse(connection);
    }

    /**
     * 发送DELETE请求
     *
     * @param endpoint 接口路径
     * @param headers 请求头
     * @return 响应结果字符串
     * @throws IOException 请求异常
     */
    public String doDelete(String endpoint, Map<String, String> headers) throws IOException {
        URL url = URI.create(BASE_URL + endpoint).toURL();
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 配置HTTPS连接的SSL证书（强化版）
        if (connection instanceof HttpsURLConnection) {
            HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
            try {
                // 确保SSL上下文已初始化
                SSLCertificateManager.initializeSSLContext();

                // 强制设置SSL配置
                forceSSLConfiguration(httpsConnection);

                System.out.println("DELETE请求SSL配置成功");
            } catch (Exception e) {
                System.err.println("DELETE请求SSL配置失败: " + e.getMessage());
                e.printStackTrace();
            }
        }

        connection.setRequestMethod("DELETE");

        // 如果headers为null，创建一个新的Map
        if (headers == null) {
            headers = new HashMap<>();
        }

        // 添加Authorization Cookie
        addAuthorizationCookie(headers, authToken);

        // 添加请求头
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            connection.setRequestProperty(entry.getKey(), entry.getValue());
        }

        return getResponse(connection);
    }

    /**
     * 获取响应结果
     *
     * @param connection HTTP连接
     * @return 响应结果字符串
     * @throws IOException 请求异常
     */
    private String getResponse(HttpURLConnection connection) throws IOException {
        int responseCode = connection.getResponseCode();
        StringBuilder response = new StringBuilder();

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(
                        responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream(),
                        StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
        }

        return response.toString();
    }

    /**
     * 获取基础URL
     *
     * @return 基础URL
     */
    public String getBaseUrl() {
        return BASE_URL;
    }

    /**
     * 添加Authorization Cookie到请求头中
     *
     * @param headers 请求头Map
     * @param token 授权令牌
     */
    private void addAuthorizationCookie(Map<String, String> headers, String token) {
        if (token != null && !token.isEmpty() && !headers.containsKey("Cookie")) {
            headers.put("Cookie", "Authorization=" + token);
        }
    }

    /**
     * 强制设置SSL配置（针对打包后的环境）
     */
    private void forceSSLConfiguration(HttpsURLConnection connection) {
        try {
            // 创建完全信任的SSL配置
            javax.net.ssl.TrustManager[] trustAllCerts = new javax.net.ssl.TrustManager[] {
                new javax.net.ssl.X509TrustManager() {
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return new java.security.cert.X509Certificate[0];
                    }
                    public void checkClientTrusted(java.security.cert.X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(java.security.cert.X509Certificate[] certs, String authType) {}
                }
            };

            // 创建SSL上下文
            javax.net.ssl.SSLContext sslContext = javax.net.ssl.SSLContext.getInstance("TLS");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

            // 强制设置到连接
            connection.setSSLSocketFactory(sslContext.getSocketFactory());
            connection.setHostnameVerifier((hostname, session) -> true);

            System.out.println("强制SSL配置已应用");

        } catch (Exception e) {
            System.err.println("强制SSL配置失败: " + e.getMessage());
            // 尝试使用SSLCertificateManager的配置作为备选
            try {
                connection.setSSLSocketFactory(SSLCertificateManager.getSSLSocketFactory());
                connection.setHostnameVerifier(SSLCertificateManager.getHostnameVerifier());
                System.out.println("使用备选SSL配置成功");
            } catch (Exception fallbackException) {
                System.err.println("备选SSL配置也失败: " + fallbackException.getMessage());
            }
        }
    }

    /**
     * 使用保存的令牌发送请求
     *
     * @param endpoint 接口路径
     * @param method 请求方法（GET, POST, PUT, DELETE）
     * @param jsonBody JSON格式的请求体（仅用于POST和PUT请求）
     * @return 响应结果字符串
     * @throws IOException 请求异常
     */
    public String sendRequestWithToken(String endpoint, String method, String jsonBody) throws IOException {
        Map<String, String> headers = new HashMap<>();

        // 添加Authorization Cookie
        addAuthorizationCookie(headers, authToken);

        return switch (method.toUpperCase()) {
            case "GET" -> doGet(endpoint, null, headers);
            case "POST" -> doPost(endpoint, jsonBody, headers);
            case "PUT" -> doPut(endpoint, jsonBody, headers);
            case "DELETE" -> doDelete(endpoint, headers);
            default -> throw new IllegalArgumentException("不支持的HTTP方法: " + method);
        };
    }
}
