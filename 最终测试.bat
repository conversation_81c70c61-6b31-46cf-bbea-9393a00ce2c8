@echo off
echo ===== 最终SSL握手问题修复测试 =====
echo.

cd /d "D:\JavaFx\Test\extension\target\io661"

echo 当前目录: %CD%
echo.

echo 检查打包文件...
if exist "io661.exe" (
    echo ✓ io661.exe 存在
) else (
    echo ✗ io661.exe 不存在
    pause
    exit /b 1
)

if exist "jre\bin\java.exe" (
    echo ✓ JRE 存在
) else (
    echo ✗ JRE 不存在
    pause
    exit /b 1
)

echo.
echo ===== 修复内容总结 =====
echo.
echo 🔧 SSL握手问题修复:
echo   ✓ 强制设置SSL系统属性
echo   ✓ 多协议SSL上下文初始化
echo   ✓ 每次HTTPS请求强制SSL配置
echo   ✓ 完全信任的证书管理器
echo   ✓ 设置默认SSL上下文
echo.
echo 🎯 验证码发送优化:
echo   ✓ 60秒频率控制
echo   ✓ 友好错误信息显示
echo   ✓ 服务器响应解析
echo   ✓ 手动JSON构建（避免依赖问题）
echo.
echo 📱 测试手机号: 17623292397
echo.
echo ===== 启动应用程序 =====
echo.
echo 现在启动修复后的应用程序...
echo 请按照以下步骤测试:
echo.
echo 1. 输入测试手机号: 17623292397
echo 2. 点击"发送验证码"按钮
echo 3. 观察是否还出现"handshake_failure"错误
echo 4. 如果成功，应该看到验证码发送成功的提示
echo 5. 如果失败，应该看到具体的错误信息（而不是SSL握手错误）
echo.
echo 预期结果:
echo - ✅ 不再出现"Received fatal alert: handshake_failure"
echo - ✅ 能够正常进行HTTPS通信
echo - ✅ 显示友好的错误提示信息
echo - ✅ 60秒内防止重复发送
echo.

start "IO661 Extension - SSL修复版" io661.exe

echo.
echo 应用程序已启动！
echo.
echo 如果仍有问题，请检查:
echo 1. 网络连接是否正常
echo 2. 防火墙是否阻止了应用程序
echo 3. 应用程序控制台输出的详细日志
echo.
echo 修复技术要点:
echo - 强制SSL系统属性设置
echo - 多重SSL协议支持 (TLS, TLSv1.2, TLSv1.3, SSL)
echo - 每次连接都重新配置SSL
echo - 完全信任的证书验证
echo - 设置默认SSL上下文
echo.

pause
