import javax.net.ssl.*;
import java.io.*;
import java.net.*;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.KeyStore;
import java.util.Collection;

public class test_ssl_detailed {
    
    public static void main(String[] args) {
        System.out.println("=== SSL/HTTPS 连接详细测试 ===");
        
        // 设置系统属性
        System.setProperty("javax.net.ssl.trustStore", "");
        System.setProperty("javax.net.ssl.trustStorePassword", "");
        System.setProperty("com.sun.net.ssl.checkRevocation", "false");
        System.setProperty("sun.security.ssl.allowUnsafeRenegotiation", "true");
        
        // 启用SSL调试
        System.setProperty("javax.net.debug", "ssl,handshake");
        
        System.out.println("1. 系统属性设置完成");
        
        // 测试证书加载
        testCertificateLoading();
        
        // 测试SSL上下文创建
        testSSLContextCreation();
        
        // 测试HTTPS连接
        testHttpsConnection();
    }
    
    private static void testCertificateLoading() {
        System.out.println("\n2. 测试证书文件加载...");
        
        try {
            InputStream certInputStream = test_ssl_detailed.class.getResourceAsStream("/cert/fullchain.pem");
            if (certInputStream != null) {
                System.out.println("✓ 证书文件从classpath加载成功");
                
                CertificateFactory cf = CertificateFactory.getInstance("X.509");
                Collection<? extends java.security.cert.Certificate> certificates = cf.generateCertificates(certInputStream);
                System.out.println("✓ 证书解析成功，共 " + certificates.size() + " 个证书");
                
                for (java.security.cert.Certificate cert : certificates) {
                    if (cert instanceof X509Certificate) {
                        X509Certificate x509 = (X509Certificate) cert;
                        System.out.println("  - 证书主题: " + x509.getSubjectX500Principal());
                        System.out.println("  - 证书颁发者: " + x509.getIssuerX500Principal());
                    }
                }
                
                certInputStream.close();
            } else {
                System.out.println("✗ 无法从classpath加载证书文件");
            }
        } catch (Exception e) {
            System.out.println("✗ 证书加载失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testSSLContextCreation() {
        System.out.println("\n3. 测试SSL上下文创建...");
        
        try {
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };
            
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
            
            System.out.println("✓ SSL上下文创建成功");
            System.out.println("  - 协议: " + sslContext.getProtocol());
            
            // 设置为默认SSL上下文
            HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
            
            System.out.println("✓ 默认SSL配置设置成功");
            
        } catch (Exception e) {
            System.out.println("✗ SSL上下文创建失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testHttpsConnection() {
        System.out.println("\n4. 测试HTTPS连接...");
        
        String[] testUrls = {
            "https://io661.com/",
            "https://www.baidu.com/",
            "https://httpbin.org/get"
        };
        
        for (String testUrl : testUrls) {
            System.out.println("\n测试连接: " + testUrl);
            
            try {
                URL url = new URL(testUrl);
                HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
                
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);
                connection.setRequestProperty("User-Agent", "IO661Extension/1.0");
                
                int responseCode = connection.getResponseCode();
                System.out.println("✓ 连接成功，响应码: " + responseCode);
                
                if (responseCode == 200) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    String line = reader.readLine();
                    if (line != null && line.length() > 50) {
                        line = line.substring(0, 50) + "...";
                    }
                    System.out.println("  响应内容: " + line);
                    reader.close();
                }
                
                connection.disconnect();
                
            } catch (Exception e) {
                System.out.println("✗ 连接失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
}
