import javax.net.ssl.*;
import java.io.*;
import java.net.*;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;

public class test_send_code {
    
    private static final String BASE_URL = "https://io661.com/";
    
    public static void main(String[] args) {
        System.out.println("=== 测试发送验证码功能 ===");
        
        // 设置系统属性
        System.setProperty("javax.net.ssl.trustStore", "");
        System.setProperty("javax.net.ssl.trustStorePassword", "");
        System.setProperty("com.sun.net.ssl.checkRevocation", "false");
        System.setProperty("sun.security.ssl.allowUnsafeRenegotiation", "true");
        
        // 配置SSL
        setupSSL();
        
        // 测试发送验证码
        testSendCode("13800138000"); // 使用测试手机号
    }
    
    private static void setupSSL() {
        try {
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };
            
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
            
            // 设置为默认SSL上下文
            HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
            
            System.out.println("✓ SSL配置完成");
            
        } catch (Exception e) {
            System.err.println("✗ SSL配置失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testSendCode(String phone) {
        System.out.println("\n测试发送验证码到手机号: " + phone);
        
        try {
            // 构建请求URL
            String endpoint = "web/sms";
            URL url = new URL(BASE_URL + endpoint);
            
            System.out.println("请求URL: " + url);
            
            // 创建连接
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("User-Agent", "IO661Extension/1.0");
            connection.setDoOutput(true);
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            
            // 构建请求体
            String jsonBody = String.format("{\"phone\":\"%s\",\"type\":0}", phone);
            System.out.println("请求体: " + jsonBody);
            
            // 发送请求
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonBody.getBytes("utf-8");
                os.write(input, 0, input.length);
            }
            
            // 获取响应
            int responseCode = connection.getResponseCode();
            System.out.println("响应码: " + responseCode);
            
            // 读取响应内容
            StringBuilder response = new StringBuilder();
            InputStream inputStream = responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream();
            
            if (inputStream != null) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "utf-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                }
            }
            
            System.out.println("响应内容: " + response.toString());
            
            // 分析结果
            if (responseCode == 200) {
                System.out.println("✓ 请求发送成功");
                
                // 简单解析响应
                String responseStr = response.toString();
                if (responseStr.contains("\"code\":0") || responseStr.contains("\"success\":true")) {
                    System.out.println("✓ 验证码发送成功");
                } else if (responseStr.contains("\"code\":") || responseStr.contains("\"error\":")) {
                    System.out.println("⚠ 服务器返回错误: " + responseStr);
                } else {
                    System.out.println("? 响应格式未知: " + responseStr);
                }
            } else {
                System.out.println("✗ HTTP请求失败，响应码: " + responseCode);
                System.out.println("错误响应: " + response.toString());
            }
            
            connection.disconnect();
            
        } catch (Exception e) {
            System.err.println("✗ 发送验证码失败: " + e.getMessage());
            e.printStackTrace();
            
            // 详细分析异常类型
            if (e instanceof ConnectException) {
                System.err.println("连接异常：可能是网络问题或服务器不可达");
            } else if (e instanceof SocketTimeoutException) {
                System.err.println("超时异常：请求超时");
            } else if (e instanceof SSLException) {
                System.err.println("SSL异常：HTTPS握手失败");
            } else if (e instanceof UnknownHostException) {
                System.err.println("域名解析异常：无法解析 io661.com");
            } else {
                System.err.println("其他异常：" + e.getClass().getSimpleName());
            }
        }
    }
}
