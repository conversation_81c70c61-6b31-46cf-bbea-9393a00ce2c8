@echo off
echo ===== 测试打包后的HTTPS连接 =====
echo.

cd /d "D:\JavaFx\Test\extension\target\io661"

echo 当前目录: %CD%
echo.

echo 检查文件是否存在:
if exist "io661.exe" (
    echo ✓ io661.exe 存在
) else (
    echo ✗ io661.exe 不存在
    pause
    exit /b 1
)

if exist "jre\bin\java.exe" (
    echo ✓ JRE 存在
) else (
    echo ✗ JRE 不存在
    pause
    exit /b 1
)

echo.
echo 启动应用程序进行HTTPS测试...
echo 请观察控制台输出中的SSL配置信息
echo.

start "IO661 Extension" io661.exe

echo.
echo 应用程序已启动，请检查:
echo 1. 应用程序是否正常启动
echo 2. 控制台是否显示SSL配置成功信息
echo 3. 是否能正常进行HTTPS请求
echo.
echo 如果遇到问题，请查看应用程序的日志输出
echo.

pause
