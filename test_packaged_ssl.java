import javax.net.ssl.*;
import java.io.*;
import java.net.*;
import java.security.cert.X509Certificate;
import java.security.SecureRandom;

public class test_packaged_ssl {
    
    private static final String BASE_URL = "https://io661.com/";
    private static final String TEST_PHONE = "17623292397";
    
    public static void main(String[] args) {
        System.out.println("=== 测试打包后的SSL配置 ===");
        System.out.println("测试手机号: " + TEST_PHONE);
        
        // 强制设置SSL系统属性
        forceSSLConfiguration();
        
        // 配置SSL
        setupAdvancedSSL();
        
        // 测试发送验证码
        testSendCodeInPackagedEnv();
    }
    
    private static void forceSSLConfiguration() {
        System.out.println("\n1. 强制设置SSL系统属性...");
        
        // 禁用SSL验证
        System.setProperty("javax.net.ssl.trustStore", "");
        System.setProperty("javax.net.ssl.trustStorePassword", "");
        System.setProperty("javax.net.ssl.keyStore", "");
        System.setProperty("javax.net.ssl.keyStorePassword", "");
        
        // 禁用各种SSL检查
        System.setProperty("com.sun.net.ssl.checkRevocation", "false");
        System.setProperty("sun.security.ssl.allowUnsafeRenegotiation", "true");
        System.setProperty("sun.security.ssl.allowLegacyHelloMessages", "true");
        System.setProperty("jdk.tls.allowUnsafeServerCertChange", "true");
        System.setProperty("jdk.tls.allowUnsafeRenegotiation", "true");
        
        // 设置TLS版本
        System.setProperty("https.protocols", "TLSv1.2,TLSv1.3");
        System.setProperty("jdk.tls.client.protocols", "TLSv1.2,TLSv1.3");
        
        // 禁用主机名验证
        System.setProperty("com.sun.net.ssl.checkRevocation", "false");
        
        System.out.println("✓ SSL系统属性设置完成");
    }
    
    private static void setupAdvancedSSL() {
        System.out.println("\n2. 配置高级SSL设置...");
        
        try {
            // 创建完全信任的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                        // 完全信任
                    }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        // 完全信任
                    }
                }
            };
            
            // 创建SSL上下文
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustAllCerts, new SecureRandom());
            
            // 设置为默认SSL上下文
            HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());
            
            // 设置主机名验证器（完全信任）
            HttpsURLConnection.setDefaultHostnameVerifier(new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true; // 信任所有主机名
                }
            });
            
            // 设置默认SSL上下文
            SSLContext.setDefault(sslContext);
            
            System.out.println("✓ 高级SSL配置完成");
            
        } catch (Exception e) {
            System.err.println("✗ SSL配置失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testSendCodeInPackagedEnv() {
        System.out.println("\n3. 测试发送验证码（打包环境）...");
        
        try {
            // 构建请求URL
            String endpoint = "web/sms";
            URL url = new URL(BASE_URL + endpoint);
            
            System.out.println("请求URL: " + url);
            
            // 创建连接
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            
            // 强制设置SSL配置
            SSLContext sslContext = SSLContext.getInstance("TLS");
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return new X509Certificate[0]; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };
            sslContext.init(null, trustAllCerts, new SecureRandom());
            connection.setSSLSocketFactory(sslContext.getSocketFactory());
            connection.setHostnameVerifier((hostname, session) -> true);
            
            // 设置请求属性
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("User-Agent", "IO661Extension/1.0");
            connection.setRequestProperty("Accept", "*/*");
            connection.setRequestProperty("Connection", "close");
            connection.setDoOutput(true);
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(15000);
            
            // 构建请求体
            String jsonBody = String.format("{\"phone\":\"%s\",\"type\":0}", TEST_PHONE);
            System.out.println("请求体: " + jsonBody);
            
            // 发送请求
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonBody.getBytes("utf-8");
                os.write(input, 0, input.length);
                os.flush();
            }
            
            // 获取响应
            int responseCode = connection.getResponseCode();
            String response = readResponse(connection);
            
            System.out.println("响应码: " + responseCode);
            System.out.println("响应内容: " + response);
            
            // 分析结果
            if (responseCode == 200) {
                System.out.println("✓ HTTPS请求成功");
                if (response.contains("\"code\":0")) {
                    System.out.println("✓ 验证码发送成功");
                } else {
                    System.out.println("⚠ 服务器返回业务错误: " + response);
                }
            } else {
                System.out.println("✗ HTTP请求失败，响应码: " + responseCode);
            }
            
            connection.disconnect();
            
        } catch (SSLHandshakeException e) {
            System.err.println("✗ SSL握手失败: " + e.getMessage());
            System.err.println("这通常是因为打包后的JRE缺少SSL证书或配置");
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("✗ 请求失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static String readResponse(HttpsURLConnection connection) throws IOException {
        int responseCode = connection.getResponseCode();
        InputStream inputStream = responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream();
        
        if (inputStream == null) {
            return "";
        }
        
        StringBuilder response = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "utf-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
        }
        
        return response.toString();
    }
}
