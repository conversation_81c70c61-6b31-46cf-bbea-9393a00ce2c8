@echo off
echo ===== HTTPS连接调试工具 =====
echo.

cd /d "D:\JavaFx\Test\extension\target\io661"

echo 当前目录: %CD%
echo.

echo 检查打包后的jar文件内容...
echo.

echo 使用内置JRE运行测试...
echo.

echo 启动应用程序并输出详细日志...
echo 请注意观察SSL相关的输出信息
echo.

REM 设置详细的SSL调试输出
set JAVA_OPTS=-Djavax.net.debug=ssl,handshake,record,plaintext -Djava.security.debug=access,failure -Dcom.sun.net.ssl.checkRevocation=false

echo 使用以下参数启动应用程序:
echo %JAVA_OPTS%
echo.

REM 启动应用程序并重定向输出到文件
jre\bin\java.exe %JAVA_OPTS% -jar io661-1.0-SNAPSHOT-runnable.jar > https_debug.log 2>&1 &

echo 应用程序已启动，日志输出到 https_debug.log
echo.
echo 请尝试发送验证码，然后按任意键查看日志...
pause

echo.
echo ===== 查看日志输出 =====
type https_debug.log

echo.
echo ===== 日志输出结束 =====
echo.

pause
