@echo off
echo ===== 测试修复后的应用程序 =====
echo.

cd /d "D:\JavaFx\Test\extension"

echo 1. 重新编译项目...
call .\mvnw.cmd clean package -q
if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo ✓ 编译成功
echo.

echo 2. 检查打包结果...
cd target\io661
if exist "io661.exe" (
    echo ✓ io661.exe 存在
) else (
    echo ✗ io661.exe 不存在
    pause
    exit /b 1
)

echo.
echo 3. 启动应用程序...
echo.
echo 修复内容:
echo - ✓ 添加了发送频率控制（60秒间隔）
echo - ✓ 改进了错误信息显示
echo - ✓ 优化了请求头设置
echo - ✓ 修复了Gson依赖问题
echo - ✓ 增强了响应解析
echo.
echo 现在启动应用程序，请测试发送验证码功能：
echo 1. 输入有效的手机号（11位数字）
echo 2. 点击"发送验证码"按钮
echo 3. 观察错误提示信息是否更加友好
echo 4. 如果提示"发送过于频繁"，请等待60秒后重试
echo.

start "IO661 Extension - 修复版" io661.exe

echo 应用程序已启动！
echo.
echo 预期行为：
echo - 如果服务器返回"发信过于频繁"，应用会显示友好的等待提示
echo - 如果服务器返回其他错误，会显示具体的错误信息
echo - 60秒内不能重复发送验证码
echo.

pause
