package com.io661.extension.util;

import javax.net.ssl.*;
import java.io.FileInputStream;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;

/**
 * SSL证书管理器
 * 用于配置自定义SSL证书以避免HTTPS握手失败
 */
public class SSLCertificateManager {

    private static SSLContext sslContext;
    private static boolean initialized = false;

    /**
     * 初始化SSL上下文，加载自定义证书
     */
    public static synchronized void initializeSSLContext() {
        if (initialized) {
            return;
        }

        try {
            // 直接创建信任所有证书的TrustManager，这样可以避免证书验证问题
            createTrustAllSSLContext();
            System.out.println("SSL证书配置成功 - 已启用信任所有证书模式");
        } catch (Exception e) {
            System.err.println("SSL证书配置失败: " + e.getMessage());
        }
    }

    /**
     * 加载自定义证书
     */
    private static void loadCustomCertificates() throws Exception {
        // 创建证书工厂
        CertificateFactory cf = CertificateFactory.getInstance("X.509");

        // 加载证书文件
        InputStream certInputStream = SSLCertificateManager.class.getResourceAsStream("/cert/fullchain.pem");
        if (certInputStream == null) {
            // 如果资源文件不存在，尝试从文件系统加载
            certInputStream = new FileInputStream("src/main/resources/cert/fullchain.pem");
        }

        // 读取所有证书（证书链）
        java.util.Collection<? extends Certificate> certificates = cf.generateCertificates(certInputStream);
        certInputStream.close();

        // 获取默认的TrustStore
        KeyStore defaultKeyStore = KeyStore.getInstance(KeyStore.getDefaultType());
        defaultKeyStore.load(null, null);

        // 获取系统默认的TrustManager
        TrustManagerFactory defaultTmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        defaultTmf.init((KeyStore) null); // 使用系统默认的TrustStore

        // 创建新的KeyStore，包含系统证书和自定义证书
        KeyStore combinedKeyStore = KeyStore.getInstance(KeyStore.getDefaultType());
        combinedKeyStore.load(null, null);

        // 添加自定义证书
        int i = 0;
        for (Certificate cert : certificates) {
            combinedKeyStore.setCertificateEntry("custom_cert" + i, cert);
            i++;
            System.out.println("已添加自定义证书: " + ((X509Certificate) cert).getSubjectX500Principal());
        }

        // 创建组合的TrustManager
        TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        tmf.init(combinedKeyStore);

        // 创建SSL上下文
        sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, tmf.getTrustManagers(), null);

        initialized = true;
        System.out.println("SSL证书配置成功，共加载 " + certificates.size() + " 个自定义证书");
    }

    /**
     * 创建信任所有证书的SSL上下文（仅用于开发环境）
     */
    private static void createTrustAllSSLContext() throws Exception {
        TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
        };

        sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
        initialized = true;
    }

    /**
     * 获取配置好的SSL上下文
     */
    public static SSLContext getSSLContext() {
        if (!initialized) {
            initializeSSLContext();
        }
        return sslContext;
    }

    /**
     * 获取配置好的SSLSocketFactory
     */
    public static SSLSocketFactory getSSLSocketFactory() {
        return getSSLContext().getSocketFactory();
    }

    /**
     * 获取配置好的HostnameVerifier
     */
    public static HostnameVerifier getHostnameVerifier() {
        // 对于io661.com域名，我们信任证书
        return (hostname, session) -> {
            if ("io661.com".equals(hostname)) {
                return true;
            }
            // 对于其他域名，使用默认验证
            return HttpsURLConnection.getDefaultHostnameVerifier().verify(hostname, session);
        };
    }
}
